// 数据归一化配置驱动方案
// 基于 demo.md 中的思路实现
// primitive 类型字段需要添加默认值

import { weekStrategyConfig } from './weekStrategyConfig';
import { weekAnalysisConfig } from './weekAnalysisConfig';
import { realAdsResultConfig } from './realAdsResultConfig';
import { marketTrendsConfig } from './marketTrendsConfig';
import { dayStrategyConfig } from './dayStrategyConfig';
import { dailyAdjustmentProposalConfig } from './dailyAdjustmentProposalConfig';
import type { Mapping } from './dataTransformer.types';

// 辅助函数，用于安全地按路径获取值
function getValueByPath(obj: any, path: string): any {
  if (!path) return obj; // 如果路径为空，返回整个对象
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// 强大的递归归一化引擎
export function normalizeDataRecursively<T>(sourceData: any, config: { [K in keyof T]: Mapping }): T {
  const normalized = {} as T;

  for (const key in config) {
    if (Object.prototype.hasOwnProperty.call(config, key)) {
      const mapping = config[key as keyof T];
      const { path, defaultValue, transformer } = mapping;

      let rawValue = getValueByPath(sourceData, path);

      // 如果值不存在，使用默认值
      if (rawValue === undefined || rawValue === null) {
        rawValue = defaultValue;
      }

      let finalValue = rawValue;

      // 根据映射类型进行处理
      //  'primitive' | 'object' | 'array'
      switch (mapping.type) {
        case 'primitive':
          // 如果有转换器，则使用它
          if (transformer) {
            finalValue = transformer(rawValue, sourceData);
          }
          break;

        case 'object':
          if (transformer) {
            finalValue = transformer(rawValue, sourceData);
          } else if (typeof rawValue === 'object' && rawValue !== null && !Array.isArray(rawValue)) {
            // 如果源值是对象，则递归地进行归一化
            finalValue = normalizeDataRecursively(rawValue, mapping.schema);
          } else {
            // 如果源值不是对象（或为null），则使用默认值或递归处理一个空对象以确保结构完整
            finalValue = normalizeDataRecursively(defaultValue || {}, mapping.schema);
          }
          break;

        case 'array':
          if (transformer) {
            finalValue = transformer(rawValue, sourceData);
          } else if (Array.isArray(rawValue)) {
            // 如果源值是数组，则遍历并对每个元素进行归一化
            if (mapping.schema) {
              // 有 schema 的情况，对每个元素进行递归处理
              finalValue = rawValue.map(item => normalizeDataRecursively(item, mapping.schema!));
            } else {
              // 没有 schema 的情况，数组元素是原始值，直接返回
              finalValue = rawValue;
            }
          } else {
            // 如果不是数组，则返回默认值（通常是空数组）
            finalValue = defaultValue ?? [];
          }
          break;
      }

      normalized[key as keyof T] = finalValue;
    }
  }

  return normalized;
}

export {
  weekStrategyConfig,
  weekAnalysisConfig,
  realAdsResultConfig,
  marketTrendsConfig,
  dayStrategyConfig,
  dailyAdjustmentProposalConfig,
};