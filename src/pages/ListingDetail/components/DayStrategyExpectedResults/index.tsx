import { Card, Row, Col, Statistic } from 'antd';
import React from 'react';

import { processNumberOrString } from '@/utils/bus';
import { useModel } from 'umi';
// const { Text } = Typography;

// 今日拖放策略预期结果
const DayStrategyExpectedResults = (props: { expected_results?: { spend: number, sales: number, acos: number, cvr: number; orders: number; }, real_ads_result: Strategy.Real_ads_result | undefined; }) => {
  const { expected_results } = props;
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  return (
    <Row gutter={16}>
      <Col span={6}>
        <Card data-test-id="day-strategy-expected-results-spend" className="card" style={{ minHeight: 130 }}>
          <Statistic
            title={<div style={{ fontSize: '14px', color: '#8c8c8c' }}>预期花费</div>}
            valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
            value={expected_results ? expected_results.spend : 0}
            prefix={currency}
            precision={2}
          />
          {/* TODO: 暂时注释掉 */}
          {/* {
            real_ads_result && (
              <div style={{ marginTop: 14, paddingTop: 12, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>当前花费</Text>
                <Text style={{ fontSize: '14px', color: '#333', fontWeight: 'bold' }}>
                  {real_ads_result.spend !== null ? currency + real_ads_result.spend : '-'}
                </Text>
              </div>
            )
          } */}
        </Card>
      </Col>
      <Col span={6}>
        <Card data-test-id="day-strategy-expected-results-sales" className="card" style={{ minHeight: 130 }}>
          <Statistic
            title={<div style={{ fontSize: '14px', color: '#8c8c8c' }}>预期销售额</div>}
            valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
            value={expected_results ? expected_results.sales : 0}
            prefix={currency}
            precision={2}
          />
          {/* TODO: 暂时注释掉 */}
          {/* {
            real_ads_result && (
              <div style={{ marginTop: 14, paddingTop: 12, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>当前销售额</Text>
                <Text style={{ fontSize: '14px', color: '#333', fontWeight: 'bold' }}>
                  {real_ads_result.sales !== null ? currency + real_ads_result.sales.toFixed(2) : '-'}
                </Text>
              </div>
            )
          } */}
        </Card>
      </Col>
      <Col span={6}>
        <Card data-test-id="day-strategy-expected-results-acos" className="card" style={{ minHeight: 130 }}>
          <Statistic
            title={<div style={{ fontSize: '14px', color: '#8c8c8c' }}>预期ACOS</div>}
            valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
            value={processNumberOrString(expected_results?.acos)}
            suffix="%"
            precision={2}
          />
          {/* TODO: 暂时注释掉 */}
          {/* {
            real_ads_result && (
              <div style={{ marginTop: 14, paddingTop: 12, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>当前ACOS</Text>
                <Text style={{ fontSize: '14px', color: '#333', fontWeight: 'bold' }}>
                  {real_ads_result.acos !== null ? (real_ads_result.acos).toFixed(2) : '-'}%
                </Text>
              </div>
            )
          } */}
        </Card>
      </Col>
      <Col span={6}>
        <Card data-test-id="day-strategy-expected-results-cvr" className="card" style={{ minHeight: 130 }}>
          <Statistic
            title={<div style={{ fontSize: '14px', color: '#8c8c8c' }}>预期转化率</div>}
            valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
            value={processNumberOrString(expected_results?.cvr)}
            suffix="%"
            precision={2}
          />
          {/* TODO: 暂时注释掉 */}
          {/* {
            real_ads_result && (
              <div style={{ marginTop: 14, paddingTop: 16, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>当前转化率</Text>
                <Text style={{ fontSize: '14px', color: '#333', fontWeight: 'bold' }}>
                  {real_ads_result.cvr !== null ? (real_ads_result.cvr).toFixed(2) : '-'}%
                </Text>
              </div>
            )
          } */}
        </Card>
      </Col>
    </Row>
  )
}

export default DayStrategyExpectedResults;