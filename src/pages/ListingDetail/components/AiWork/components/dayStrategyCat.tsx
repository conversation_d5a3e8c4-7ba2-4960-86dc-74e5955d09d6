import React, { useState, useEffect } from 'react';
import { Card, Table, Typography, Flex, message, Empty, Button, Row, Col, Tag, Tooltip } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';
import { getBaseReport, getDaypartReports } from '@/services/ibidder_api/operation';
import { approachDay, approachDayColor, getBudgetColor, getBudgetColorTag, processNumber } from '@/utils/bus';
import DailyProgressAnalysis from './DailyProgressAnalysis';
import { processNumberOrString } from '@/utils/bus';
import { useSearchParams, useModel } from '@umijs/max';
import { normalizeDataRecursively, dailyAdjustmentProposalConfig } from '@/utils/dataTransformer';
import HodChart from './HodChart';
import { getCampaignTypeColor } from '../../bus';
import { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

// 组件的 Props
interface DayStrategyCatContentProps {
  job_id?: string;
  current_time?: string;
  target_job_id?: string;
  onTitleChange?: (title: string) => void;
}

const DayStrategyCatContent: React.FC<DayStrategyCatContentProps> = ({
  job_id,
  current_time,
  target_job_id,
  onTitleChange,
}) => {
  const [searchParams] = useSearchParams();
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  const country = productInfo?.country;
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const [proposal, setProposal] = useState<Strategy.DailyAdjustmentProposal | undefined>();
  const [daypartReports, setDaypartReports] = useState<any[]>([]);
  const [selectedTimeIndex, setSelectedTimeIndex] = useState<number>(0);
  const [hodBidding, setHodBidding] = useState<API.TAds_data_hod[]>([]);

  // 获取广告优化调整数据
  const fetchStrategyData = async () => {
    if (asin && profile_id && job_id && current_time) {
      try {
        const res: any = await getBaseReport({
          job_id,
          asin,
          profile_id,
          current_time,
          target_job_id
        });
        if (res.data && res.data.result && (res.data.result.daypart_strategy || res.data.result.ads_strategy_daypart)) {
          const strategyData = res.data.result.daypart_strategy || res.data.result.ads_strategy_daypart;
          const rawProposal = strategyData?.daily_adjustment_proposal;
          if (rawProposal) {
            console.log(888,rawProposal.adjustments.campaign)
            // 使用归一化配置处理数据
            const normalizedProposal = normalizeDataRecursively<Strategy.DailyAdjustmentProposal>(
              rawProposal,
              dailyAdjustmentProposalConfig
            );
            setProposal(normalizedProposal);
          } else {
            setProposal(undefined);
          }
          // 设置弹框标题
          if (onTitleChange && res.data.target_week) {
            const title = `广告优化调整（${res.data.target_week}）`;
            onTitleChange(title);
          }
        }
      } catch (error) {
        console.error('获取广告优化调整数据失败:', error);
        message.error('获取数据失败，请重试');
      }
    }
  };

  const fetchDaypartReports = async () => {
    const res: any = await getDaypartReports({
      asin,
      profile_id: profile_id ? parseInt(profile_id) : undefined,
      current_time
    })
    if (res.code === 200) {
      setDaypartReports(res.data.daypart_data || [])
      setHodBidding(res.data.ads_data_hod || [])
    }
  }

  // 处理按钮点击事件
  const handleTimeButtonClick = (index: number) => {
    setSelectedTimeIndex(index);
    const selectedItem = daypartReports[index];

    // 检查 result.ads_strategy_daypart 是否为 undefined、null 或空对象
    const adsStrategyDaypart = selectedItem?.result?.ads_strategy_daypart;

    if (!adsStrategyDaypart ||
      adsStrategyDaypart === null ||
      (typeof adsStrategyDaypart === 'object' && Object.keys(adsStrategyDaypart).length === 0)) {
      // 如果是 undefined、null 或空对象，设置为 undefined 以显示"暂无数据"
      setProposal(undefined);
    } else {
      // 有有效数据时，使用归一化配置处理数据
      const rawProposal = adsStrategyDaypart.daily_adjustment_proposal;
      if (rawProposal) {
        const normalizedProposal = normalizeDataRecursively<Strategy.DailyAdjustmentProposal>(
          rawProposal,
          dailyAdjustmentProposalConfig
        );
        setProposal(normalizedProposal);
      } else {
        setProposal(undefined);
      }
    }
  };
  // 格式化时间显示
  const formatTimeDisplay = (currentTime: string, country?: string) => {
    // 提取时间部分 (HH:MM)
    const timePart = currentTime.split(' ')[1] || '';
    // 获取站点代码，默认为传入的country或'US'
    const siteCode = country || 'US';
    return `${timePart} ${siteCode}`;
  };

  useEffect(() => {
    fetchStrategyData();
    fetchDaypartReports();
  }, [asin, profile_id, job_id, current_time]);

  // 当 daypartReports 数据加载完成后，根据 current_time 匹配对应的时间按钮
  useEffect(() => {
    if (daypartReports.length > 0 && current_time) {
      // 从 current_time 中提取时间部分 (HH:MM)
      const currentTimePart = current_time.split(' ')[1] || '';

      // 查找匹配的时间按钮索引
      const matchingIndex = daypartReports.findIndex(item => {
        const itemTimePart = item.current_time.split(' ')[1] || '';
        return itemTimePart === currentTimePart;
      });

      // 如果找到匹配的时间，选择该按钮；否则默认选择第一个
      const selectedIndex = matchingIndex >= 0 ? matchingIndex : 0;
      handleTimeButtonClick(selectedIndex);
    } else if (daypartReports.length > 0) {
      // 如果没有 current_time，默认选择第一个
      handleTimeButtonClick(0);
    }
  }, [daypartReports, current_time]);

  const campaignColumns: ColumnsType<API.CampaignAdjustmentItem> = [
    {
      title: '广告类型',
      dataIndex: 'campaign_type',
      key: 'campaign_type',
      width: 88,
      fixed: 'left' as const,
      render: (text: string) => {
        if (text === null || text === undefined) return '-';
        return (
          <Tag color={getCampaignTypeColor(text)}>{text.toUpperCase()}</Tag>
        );
      }
    },
    {
      title: '广告活动',
      dataIndex: 'campaign_name',
      key: 'campaign_name',
      width: 200,
    },
    {
      title: '当前表现',
      width: 120,
      key: 'current_performance',
      sorter: (a, b) => {
        const spendA = a.current_spend ? Number(a.current_spend) : 0;
        const spendB = b.current_spend ? Number(b.current_spend) : 0;
        return spendA - spendB;
      },
      render: (_: any, record) => {
        const { current_spend, current_sales, current_acos, current_cvr } = record;
        return (
          <div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'auto auto 1fr',
              gap: '4px 8px',
              alignItems: 'center'
            }}>
              <Text type="secondary">花费</Text>
              <Text type="secondary">:</Text>
              <span>{current_spend !== '' ? `${currency}${processNumber(current_spend, 2)}` : '-'}</span>

              <Text type="secondary">销售额</Text>
              <Text type="secondary">:</Text>
              <span>{current_sales !== '' ? `${currency}${processNumber(current_sales, 2)}` : '-'}</span>

              <Text type="secondary">ACoS</Text>
              <Text type="secondary">:</Text>
              <span>{current_acos !== '' ? processNumberOrString(current_acos, '%') : '-'}</span>

              <Text type="secondary">CVR</Text>
              <Text type="secondary">:</Text>
              <span>{current_cvr !== '' ? processNumberOrString(current_cvr, '%') : '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: '预算调整',
      width: 100,
      key: 'budget',
      sorter: (a, b) => {
        const budgetNewA = a.budget_new ? Number(a.budget_new) : 0;
        const budgetNewB = b.budget_new ? Number(b.budget_new) : 0;
        return budgetNewA - budgetNewB;
      },
      render: (_: any, record) => {
        if (!record.budget_old || !record.budget_new) return <Text>-</Text>;
        const budget_old = processNumber(record.budget_old);
        const budget_new = processNumber(record.budget_new);
        return (
          <Text style={{ whiteSpace: 'nowrap' }}>
            <span style={{ color: getBudgetColor(budget_old, budget_new) }}>{currency}{budget_new}</span>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>{currency}{budget_old}（旧）</Text>
          </Text>
        );
      },
    },
    {
      title: <span>搜索结果顶部<br />竞价调整</span>,
      width: 100,
      key: 'placements',
      render: (_: any, record) => {
        if (record.bid_old === '' || record.bid_new === '') return <Text>-</Text>;
        const bid_old = record.bid_old;
        const bid_new = record.bid_new;
        return (
          <Tooltip
            title={
              <div>
                <div style={{ fontSize: 12, opacity: 0.8 }}> {daypartReports[selectedTimeIndex]?.current_time || ''}（站点时间）</div>
                <div style={{ display: 'inline-grid', gridTemplateColumns: 'auto 1fr', gap: '2px 4px', alignItems: 'center' }}>
                  <span>调整后竞价：</span>
                  <span style={{ textAlign: 'right' }}>{processNumberOrString(bid_new, '%')}</span>
                  <span>调整前竞价：</span>
                  <span style={{ textAlign: 'right' }}>{processNumberOrString(bid_old, '%')}</span>
                </div>
              </div>
            }
          >
            <Text style={{ whiteSpace: 'nowrap' }}>
              <Tag color={getBudgetColorTag(bid_old, bid_new)}>{processNumberOrString(bid_new, '%')}</Tag>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>{processNumberOrString(bid_old, '%')}（旧）</Text>
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: <span>商品页面<br />竞价调整</span>,
      width: 100,
      key: 'placements',
      render: (_: any, record) => {
        if (record.bid_old === '' || record.bid_new === '') return <Text>-</Text>;
        const bid_old = record.bid_old;
        const bid_new = record.bid_new;
        return (
          <Tooltip
            title={
              <div>
                <div style={{ fontSize: 12, opacity: 0.8 }}> {daypartReports[selectedTimeIndex]?.current_time || ''}（站点时间）</div>
                <div style={{ display: 'inline-grid', gridTemplateColumns: 'auto 1fr', gap: '2px 4px', alignItems: 'center' }}>
                  <span>调整后竞价：</span>
                  <span style={{ textAlign: 'right' }}>{processNumberOrString(bid_new, '%')}</span>
                  <span>调整前竞价：</span>
                  <span style={{ textAlign: 'right' }}>{processNumberOrString(bid_old, '%')}</span>
                </div>
              </div>
            }
          >
            <Text style={{ whiteSpace: 'nowrap' }}>
              <Tag color={getBudgetColorTag(bid_old, bid_new)}>{processNumberOrString(bid_new, '%')}</Tag>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>{processNumberOrString(bid_old, '%')}（旧）</Text>
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: <span>搜索结果其余<br />位置竞价调整</span>,
      width: 100,
      key: 'placements',
      render: (_: any, record) => {
        if (record.bid_old === '' || record.bid_new === '') return <Text>-</Text>;
        const bid_old = record.bid_old;
        const bid_new = record.bid_new;
        return (
          <Tooltip
            title={
              <div>
                <div style={{ fontSize: 12, opacity: 0.8 }}> {daypartReports[selectedTimeIndex]?.current_time || ''}（站点时间）</div>
                <div style={{ display: 'inline-grid', gridTemplateColumns: 'auto 1fr', gap: '2px 4px', alignItems: 'center' }}>
                  <span>调整后竞价：</span>
                  <span style={{ textAlign: 'right' }}>{processNumberOrString(bid_new, '%')}</span>
                  <span>调整前竞价：</span>
                  <span style={{ textAlign: 'right' }}>{processNumberOrString(bid_old, '%')}</span>
                </div>
              </div>
            }
          >
            <Text style={{ whiteSpace: 'nowrap' }}>
              <Tag color={getBudgetColorTag(bid_old, bid_new)}>{processNumberOrString(bid_new, '%')}</Tag>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>{processNumberOrString(bid_old, '%')}（旧）</Text>
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: '调整理由',
      dataIndex: 'rationale',
      width: '33%',
      key: 'rationale',
      render: (rationale: string) => {
        return <Text>{rationale || '-'}</Text>;
      },
    },
  ];

  const budgetValueStyle: React.CSSProperties = {
    fontSize: '28px',
    fontWeight: 'bold',
    color: '#303133',
  };

  return (
    <div className='report-modal-div'>
      <div className='report-modal-content' style={{ marginTop: '32px' }}>
        {/* 时间按钮组 - 当 daypartReports 数组长度大于1时显示 */}
        {daypartReports.length > 1 && (
          <div style={{ marginBottom: '24px' }}>
            <Row gutter={[8, 8]}>
              {daypartReports.map((item, index) => (
                <Col key={index}>
                  <Button
                    type={selectedTimeIndex === index ? 'primary' : 'default'}
                    onClick={() => handleTimeButtonClick(index)}
                  >
                    {formatTimeDisplay(item.current_time, country)}
                  </Button>
                </Col>
              ))}
            </Row>
          </div>
        )}

        {
          !proposal ?
            <Empty description="暂无数据" />
            :
            <>
              <DailyProgressAnalysis
                hodBidding={hodBidding}
                data={proposal?.day_progress_analysis}
                overallRationale={proposal.overall_rationale}
                time={daypartReports[selectedTimeIndex]?.current_time || ''}
              />

              {proposal.adjustments &&
                (proposal.adjustments?.daily_budget || (proposal.adjustments?.campaign && proposal.adjustments?.campaign?.length > 0))
                &&
                <Title level={3} style={{ marginTop: 48 }}>投放策略调整</Title>
              }
              {/* Section 2: 策略调整 */}
              {proposal.adjustments?.daily_strategy && (proposal.adjustments.daily_strategy.approach_old !== proposal.adjustments.daily_strategy.approach_new) && (
                <Card data-test-id="day-strategy-strategy-adjustment" className="card" style={{ marginBottom: '1.5em' }}>
                  <Title level={4}>策略调整</Title>
                  <Flex align="center" justify="flex-start" gap="middle" style={{ marginTop: 12, marginBottom: 12 }}>
                    <Text style={{ ...budgetValueStyle, color: approachDayColor(proposal.adjustments.daily_strategy.approach_old) }}>{approachDay(proposal.adjustments.daily_strategy.approach_old)}</Text>
                    <Flex vertical={true} gap="2px">
                      <ArrowRightOutlined style={{ fontSize: '20px', margin: '0 10px', color: '#606266' }} />
                    </Flex>
                    <Text style={{ ...budgetValueStyle, color: approachDayColor(proposal.adjustments.daily_strategy.approach_new) }}>{approachDay(proposal.adjustments.daily_strategy.approach_new)}</Text>
                  </Flex>
                  <Text style={{ whiteSpace: 'pre-line', lineHeight: '1.8', color: '#303133', fontSize: '14px', }}>
                    {proposal.adjustments.daily_strategy.rationale}
                  </Text>
                </Card>
              )}
              {/* Section 2: 预算调整 */}
              {proposal.adjustments?.daily_budget && (proposal.adjustments.daily_budget.old !== proposal.adjustments.daily_budget.new) && (
                <Card data-test-id="day-strategy-budget-adjustment" className="card" style={{ marginBottom: '1.5em' }}>
                  <Title level={4}>预算调整</Title>
                  <Flex align="center" justify="flex-start" gap="middle" style={{ marginTop: 12, marginBottom: 12 }}>
                    <Text style={budgetValueStyle}>{currency}{processNumber(proposal.adjustments.daily_budget.old, 0)}</Text>
                    <Flex vertical={true} gap="2px">
                      <ArrowRightOutlined style={{ fontSize: '20px', margin: '0 10px', color: '#606266' }} />
                    </Flex>
                    <Text style={{ ...budgetValueStyle, color: '#D9001B' }}>{currency}{processNumber(proposal.adjustments.daily_budget.new, 0)}</Text>
                  </Flex>
                  <Text style={{ whiteSpace: 'pre-line', lineHeight: '1.8', color: '#303133', fontSize: '14px', }}>
                    {proposal.adjustments.daily_budget.rationale}
                  </Text>
                </Card>
              )}

              {/* Section 3: Campaign调整 */}
              {proposal.adjustments?.campaign && proposal.adjustments.campaign?.length > 0 && (
                <>
                  <Card data-test-id="day-strategy-campaign-adjustment" className="card" style={{ marginBottom: '1.5em' }}>
                    <Title level={4}>
                      <span>Campaign调整</span>
                      <br />
                      <Text type="secondary">*实际投放时，AI 会根据实际投放表现动态调整预算和竞价，除非需要大幅度修改，一般不建议修改 Campaign 的预算和竞价</Text>
                    </Title>
                    <Table<API.CampaignAdjustmentItem>
                      columns={campaignColumns}
                      dataSource={proposal.adjustments.campaign}
                      rowKey="campaign_id"
                      pagination={false}
                      size="middle"
                      sticky
                    />
                  </Card>
                </>
              )}

              <Title level={3} style={{ marginTop: 48 }}>分时竞价调整</Title>
              <Card data-test-id="day-strategy-hod-bid-adjustment" className="card" style={{ marginTop: "16px" }}>
                {proposal?.adjustments?.hod?.length > 0 ? (
                  <HodChart
                    hodBidding={proposal?.adjustments?.hod?.map((item) => ({
                      hour: Number(item.hour),
                      adjustment: Number(item.bid_new),
                    }))}
                  />
                ) : (
                  <Empty description="暂无分时竞价数据" />
                )}
                {
                  proposal?.adjustments?.hod_rationale && (
                    <div style={{ marginTop: '10px', padding: '0 10px' }}>
                      <Title level={5}>调整理由：</Title>
                      <Text>{proposal?.adjustments?.hod_rationale}</Text>
                    </div>
                  )
                }
              </Card>
            </>
        }
      </div>
    </div>
  );
};

export default DayStrategyCatContent;
